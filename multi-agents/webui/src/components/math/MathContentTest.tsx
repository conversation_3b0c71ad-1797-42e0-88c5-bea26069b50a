import React from 'react';
import { MathContent } from './MathContent';

export const MathContentTest: React.FC = () => {
  const testCases = [
    {
      title: "基础测试 - 所有格式",
      content: "测试所有数学公式格式：\n\n行内公式：$a + b = c$ 和 \\(x = y\\)\n\n显示公式：$$E = mc^2$$\n\n还有：\\[F = ma\\]"
    },
    {
      title: "行内数学公式 - $ $ 格式",
      content: "这是一个行内公式：$x^2 + y^2 = z^2$，这是勾股定理。"
    },
    {
      title: "行内数学公式 - \\( \\) 格式",
      content: "这是另一个行内公式：\\(\\frac{a}{b} = \\frac{c}{d}\\)，这是比例关系。"
    },
    {
      title: "显示数学公式 - $$ $$ 格式",
      content: "这是一个显示公式：\n\n$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$\n\n这是高斯积分。"
    },
    {
      title: "显示数学公式 - \\[ \\] 格式",
      content: "这是另一个显示公式：\\[\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}\\]这是巴塞尔问题的解。"
    },
    {
      title: "混合格式",
      content: "混合使用不同格式：行内公式 $f(x) = ax + b$，然后是显示公式：\n\n\\[f'(x) = a\\]\n\n再来一个行内公式 \\(\\lim_{x \\to 0} \\frac{\\sin x}{x} = 1\\)。"
    },
    {
      title: "复杂数学公式",
      content: "复杂的数学表达式：\n\n\\[\\begin{align}\n\\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n\\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}\\]\n\n这是麦克斯韦方程组的一部分。"
    },
    {
      title: "矩阵和分数",
      content: "矩阵表示：\n\n$$\\begin{pmatrix}\na & b \\\\\nc & d\n\\end{pmatrix} \\begin{pmatrix}\nx \\\\\ny\n\\end{pmatrix} = \\begin{pmatrix}\nax + by \\\\\ncx + dy\n\\end{pmatrix}$$\n\n分数表示：\\(\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}\\)"
    },
    {
      title: "Markdown 格式测试",
      content: "# 标题测试\n\n## 二级标题\n\n**粗体文本** 和 *斜体文本*\n\n数学公式：$E = mc^2$\n\n- 列表项 1\n- 列表项 2 with math: \\(\\alpha + \\beta = \\gamma\\)\n\n```\n代码块测试\n```\n\n最后一个公式：\\[\\int_0^1 x^2 dx = \\frac{1}{3}\\]"
    }
  ];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">数学公式渲染测试 (ReactMarkdown + MathJax)</h1>
      
      <div className="space-y-6">
        {testCases.map((testCase, index) => (
          <div key={index} className="border rounded-lg p-4 bg-white shadow-sm">
            <h3 className="text-lg font-semibold mb-3 text-blue-700">
              {testCase.title}
            </h3>
            
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-600 mb-2">原始内容：</h4>
              <pre className="bg-gray-100 p-2 rounded text-sm overflow-x-auto whitespace-pre-wrap">
                {testCase.content}
              </pre>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">渲染结果：</h4>
              <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-3 rounded">
                <MathContent content={testCase.content} />
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="text-lg font-semibold text-green-800 mb-2">技术说明</h3>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• 使用 ReactMarkdown + remarkMath + rehypeMathjax 组合</li>
          <li>• remarkMath 识别数学公式并添加适当的类名</li>
          <li>• rehypeMathjax 在编译时渲染数学公式为 SVG</li>
          <li>• 支持所有格式：<code>$...$</code>, <code>\\(...\\)</code>, <code>$$...$$</code>, <code>\\[...\\]</code></li>
          <li>• 无需客户端 JavaScript，数学公式在服务端渲染</li>
        </ul>
      </div>
    </div>
  );
};
