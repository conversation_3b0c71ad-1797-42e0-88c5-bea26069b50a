import React from 'react';
import { MathContent } from './MathContent';

export const MathContentTest: React.FC = () => {
  const testCases = [
    {
      title: "行内数学公式 - $ $ 格式",
      content: "这是一个行内公式：$x^2 + y^2 = z^2$，这是勾股定理。"
    },
    {
      title: "行内数学公式 - \\( \\) 格式",
      content: "这是另一个行内公式：\\(\\frac{a}{b} = \\frac{c}{d}\\)，这是比例关系。"
    },
    {
      title: "显示数学公式 - $$ $$ 格式",
      content: "这是一个显示公式：\n\n$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$\n\n这是高斯积分。"
    },
    {
      title: "显示数学公式 - \\[ \\] 格式",
      content: "这是另一个显示公式：\n\n\\[\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}\\]\n\n这是巴塞尔问题的解。"
    },
    {
      title: "混合格式",
      content: "混合使用不同格式：行内公式 $f(x) = ax + b$，然后是显示公式：\n\n\\[f'(x) = a\\]\n\n再来一个行内公式 \\(\\lim_{x \\to 0} \\frac{\\sin x}{x} = 1\\)。"
    },
    {
      title: "复杂数学公式",
      content: "复杂的数学表达式：\n\n\\[\\begin{align}\n\\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n\\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}\\]\n\n这是麦克斯韦方程组的一部分。"
    },
    {
      title: "矩阵和分数",
      content: "矩阵表示：\n\n$$\\begin{pmatrix}\na & b \\\\\nc & d\n\\end{pmatrix} \\begin{pmatrix}\nx \\\\\ny\n\\end{pmatrix} = \\begin{pmatrix}\nax + by \\\\\ncx + dy\n\\end{pmatrix}$$\n\n分数表示：\\(\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}\\)"
    },
    {
      title: "边缘情况测试 - 嵌套和特殊字符",
      content: "测试特殊情况：\n\n1. 连续的公式：\\(a = b\\) 和 \\(c = d\\)\n\n2. 公式中的特殊字符：\\[\\text{if } x \\in \\{1, 2, 3\\} \\text{ then } f(x) = x^2\\]\n\n3. 混合嵌套：文本 $\\alpha$ 中间 \\(\\beta\\) 然后显示：\n\\[\\gamma = \\int_0^\\infty e^{-t} dt\\]\n\n4. 带换行的长公式：\n\\[\\begin{multline}\n(a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p)^2 \\\\\n= a^2 + b^2 + c^2 + \\cdots + p^2 + 2(ab + ac + \\cdots + op)\n\\end{multline}\\]"
    },
    {
      title: "实际应用场景",
      content: "解题步骤示例：\n\n**问题：** 求解方程 \\(x^2 - 5x + 6 = 0\\)\n\n**解答：**\n\n使用求根公式：\n\\[x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}\\]\n\n其中 \\(a = 1\\), \\(b = -5\\), \\(c = 6\\)\n\n代入得：\n\\[x = \\frac{5 \\pm \\sqrt{25 - 24}}{2} = \\frac{5 \\pm 1}{2}\\]\n\n因此：\\(x_1 = 3\\), \\(x_2 = 2\\)"
    }
  ];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">数学公式渲染测试</h1>
      
      <div className="space-y-6">
        {testCases.map((testCase, index) => (
          <div key={index} className="border rounded-lg p-4 bg-white shadow-sm">
            <h3 className="text-lg font-semibold mb-3 text-blue-700">
              {testCase.title}
            </h3>
            
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-600 mb-2">原始内容：</h4>
              <pre className="bg-gray-100 p-2 rounded text-sm overflow-x-auto">
                {testCase.content}
              </pre>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-2">渲染结果：</h4>
              <div className="border-l-4 border-blue-500 pl-4 bg-blue-50 p-3 rounded">
                <MathContent content={testCase.content} />
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">使用说明</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• 行内公式：使用 <code>$...$</code> 或 <code>\(...\)</code></li>
          <li>• 显示公式：使用 <code>$$...$$</code> 或 <code>\[...\]</code></li>
          <li>• 支持 LaTeX 语法，包括分数、矩阵、积分、求和等</li>
          <li>• 自动处理转义字符和格式转换</li>
        </ul>
      </div>
    </div>
  );
};
