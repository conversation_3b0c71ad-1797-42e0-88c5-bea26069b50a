import { useRef } from 'react';
import { MathStep } from '../../types';
import { MathProvider } from './MathProvider';
import { MathStepItem } from './MathStepItem';

interface MathStepDisplayProps {
  steps: MathStep[];
  showRawJson?: boolean;
}

export const MathStepDisplay: React.FC<MathStepDisplayProps> = ({
  steps,
  showRawJson = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   // 当有新步骤添加时，滚动到底部
  //   if (containerRef.current) {
  //     containerRef.current.scrollTop = containerRef.current.scrollHeight;
  //   }
  // }, [steps]);

  if (steps.length === 0) {
    return (
      <div className="bg-gray-50 p-8 rounded-lg text-center text-gray-500">
        等待解题结果...
      </div>
    );
  }

  return (
    <MathProvider>
      <div className="space-y-4">
        <div
          ref={containerRef}
          className="steps-container max-h-[600px] overflow-y-auto pr-2"
        >
          {steps.map((step, index) => (
            <MathStepItem key={step.step_id || index} step={step} index={index} />
          ))}
        </div>

        {showRawJson && (
          <div className="mt-6">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">原始JSON数据：</h4>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto font-mono text-xs">
              <pre>{JSON.stringify(steps, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>
    </MathProvider>
  );
};
