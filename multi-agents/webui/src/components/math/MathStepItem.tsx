import React from 'react';
import { MathStep } from '../../types';
import { MathContent } from './MathContent';

interface MathStepItemProps {
  step: MathStep;
  index: number;
}

export const MathStepItem: React.FC<MathStepItemProps> = ({ step, index }) => {

  return (
    <div
      className="step bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 p-6 mb-4 rounded-r-lg shadow-sm animate-fadeIn"
    >
      <h4 className="text-lg font-semibold text-blue-700 mb-3">
        步骤 {step.step_id || (index + 1)}
      </h4>

      <div className="space-y-4">
        <div className="step-content">
          <h5 className="font-medium text-gray-800 mb-2">问题：</h5>
          <div className="markdown-content bg-white p-3 rounded border">
            <MathContent content={step.problem} />
          </div>
        </div>

        <div className="step-content">
          <h5 className="font-medium text-gray-800 mb-2">解答：</h5>
          <div className="markdown-content bg-white p-3 rounded border">
            <MathContent content={step.content} />
          </div>
        </div>

        <div className="lecture bg-blue-100 p-4 rounded-lg">
          <h5 className="font-medium text-blue-800 mb-2">💡 讲解：</h5>
          <div className="markdown-content text-blue-700">
            <MathContent content={step.lecture} />
          </div>
        </div>
      </div>
    </div>
  );
};
