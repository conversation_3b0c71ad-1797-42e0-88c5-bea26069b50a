import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeMathjax from 'rehype-mathjax';
import remarkMath from 'remark-math';

interface MathContentProps {
  content: string;
  className?: string;
}

export const MathContent: React.FC<MathContentProps> = ({ content, className = '' }) => {
  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[rehypeMathjax]}
        components={{
          // 自定义渲染器，保持良好的样式
          p: ({ children }) => <p className="mb-2">{children}</p>,
          code: ({ children, className }) => {
            // 如果是数学公式相关的类，直接返回内容让 rehype-mathjax 处理
            if (className?.includes('language-math') || className?.includes('math-')) {
              return <code className={className}>{children}</code>;
            }
            return <code className="bg-gray-100 px-1 py-0.5 rounded text-sm">{children}</code>;
          },
          pre: ({ children }) => <pre className="bg-gray-100 p-2 rounded overflow-x-auto">{children}</pre>,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
