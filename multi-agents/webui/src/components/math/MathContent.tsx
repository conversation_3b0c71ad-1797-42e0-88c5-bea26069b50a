import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeMathJax from 'rehype-mathjax';

interface MathContentProps {
  content: string;
  className?: string;
}

export const MathContent: React.FC<MathContentProps> = ({ content, className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 当内容更新时，确保 MathJax 重新渲染
    if (containerRef.current && window.MathJax) {
      // 使用 setTimeout 确保 ReactMarkdown 渲染完成后再执行 MathJax
      const element = containerRef.current;
      setTimeout(() => {
        if (element) {
          window.MathJax.typesetPromise?.([element]).catch((err: any) => {
            console.error('MathJax typeset error:', err);
          });
        }
      }, 100);
    }
  }, [content]);

  return (
    <div ref={containerRef} className={className}>
      <ReactMarkdown
        components={{
          // 自定义渲染器，确保不干扰数学公式
          p: ({ children }) => <p className="mb-2">{children}</p>,
          code: ({ children, className }) => {
            // 避免将数学公式当作代码块处理
            if (className?.includes('language-math')) {
              return <span>{children}</span>;
            }
            return <code className="bg-gray-100 px-1 py-0.5 rounded text-sm">{children}</code>;
          },
          pre: ({ children }) => <pre className="bg-gray-100 p-2 rounded overflow-x-auto">{children}</pre>,
          // 确保文本节点不被过度处理
          text: ({ children }) => <>{children}</>,
        }}
      rehypePlugins={[rehypeMathJax]}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
