import React, { useEffect, useRef } from 'react';

interface MathContentProps {
  content: string;
  className?: string;
}

export const MathContent: React.FC<MathContentProps> = ({ content, className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理基本的 Markdown 格式（不使用 ReactMarkdown 以避免与 MathJax 冲突）
  const processMarkdown = (text: string): string => {
    return text
      // 处理标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 处理粗体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // 处理斜体
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理换行
      .replace(/\n\n/g, '</p><p>')
      // 处理单个换行
      .replace(/\n/g, '<br/>');
  };

  useEffect(() => {
    // 当内容更新时，确保 MathJax 重新渲染
    if (containerRef.current && window.MathJax) {
      window.MathJax.typesetPromise?.([containerRef.current]).catch((err: any) => {
        console.error('MathJax typeset error:', err);
      });
    }
  }, [content]);

  const processedContent = processMarkdown(content);

  return (
    <div
      ref={containerRef}
      className={className}
      dangerouslySetInnerHTML={{ __html: `<p>${processedContent}</p>` }}
    />
  );
};
