import { MathJax3Config, MathJaxContext } from 'better-react-mathjax';
import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeMathjax from 'rehype-mathjax';
import remarkMath from 'remark-math';

interface MathContentProps {
  content: string;
  className?: string;
}

export const MathContent: React.FC<MathContentProps> = ({ content, className = '' }) => {
  // 预处理内容，确保 \[ \] 和 \( \) 格式能被正确识别
  const preprocessContent = (text: string): string => {
    // 处理 \[ \] 格式的显示数学公式，转换为 $$ $$
    text = text.replace(/\\\[([\s\S]*?)\\\]/g, (_, formula) => {
      return `$$${formula.trim()}$$`;
    });

    // 处理 \( \) 格式的行内数学公式，转换为 $ $
    text = text.replace(/\\\(([\s\S]*?)\\\)/g, (_, formula) => {
      return `$${formula.trim()}$`;
    });

    // 确保显示公式前后有适当的换行
    text = text.replace(/(\$\$[\s\S]*?\$\$)/g, '\n\n$1\n\n');

    return text;
  };

  const config = {
    startup: {
      typeset: true,
    },
    tex: {
      // 配置所有常见的数学公式分隔符
      inlineMath: [
        ['$', '$'],           // 标准行内公式
        ['\\(', '\\)']        // LaTeX 行内公式
      ],
      displayMath: [
        ['$$', '$$'],         // 标准显示公式
        ['\\[', '\\]']        // LaTeX 显示公式
      ],
      processEscapes: true,   // 处理转义字符
      processEnvironments: true, // 处理 LaTeX 环境
      processRefs: true,      // 处理引用
      digits: /^(?:[0-9]+(?:\{,\}[0-9]{3})*(?:\.[0-9]*)?|\.[0-9]+)/,
      tags: 'none',           // 不显示公式编号
      tagSide: 'right',
      tagIndent: '0.8em',
      useLabelIds: true,
      multlineWidth: '85%',
      // 加载更多 TeX 扩展包
      packages: {
        '[+]': [
          'base',           // 基础包
          'ams',            // AMS 数学包
          'newcommand',     // 新命令支持
          'configmacros',   // 配置宏
          'action',         // 动作支持
          'bbox',           // 边界框
          'boldsymbol',     // 粗体符号
          'braket',         // 量子力学符号
          'cancel',         // 删除线
          'color',          // 颜色支持
          'enclose',        // 包围符号
          'extpfeil',       // 扩展箭头
          'html',           // HTML 支持
          'mathtools',      // 数学工具
          'mhchem',         // 化学公式
          'unicode',        // Unicode 支持
          'verb',           // 逐字文本
          'noerrors',       // 错误处理
          'noundefined'     // 未定义处理
        ]
      },
    },
    options: {
      // 更精确的 HTML 处理配置
      ignoreHtmlClass: 'tex2jax_ignore|mathjax_ignore',
      processHtmlClass: 'tex2jax_process|mathjax_process',
      skipHtmlTags: {
        '[-]': ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'annotation', 'annotation-xml'],
        '[+]': ['li', 'dd']
      },
      includeHtmlTags: {
        br: '\n',
        wbr: '',
        '#comment': ''
      },
      renderActions: {
        addMenu: [0, '', '']
      }
    },
    svg: {
      fontCache: 'global',
      displayAlign: 'center',
      displayIndent: '0',
      scale: 1,
      minScale: 0.5,
      mtextInheritFont: false,
      merrorInheritFont: true,
      mathmlSpacing: false,
      skipAttributes: {},
      exFactor: 0.5,
      displayOverflow: 'linebreak',
      linebreaks: { automatic: false }
    },
    loader: {
      load: [
        '[tex]/action', '[tex]/bbox', '[tex]/boldsymbol', '[tex]/braket',
        '[tex]/cancel', '[tex]/color', '[tex]/enclose', '[tex]/extpfeil',
        '[tex]/html', '[tex]/mathtools', '[tex]/mhchem', '[tex]/unicode',
        '[tex]/verb', '[tex]/noerrors', '[tex]/noundefined'
      ]
    }
  } satisfies MathJax3Config

  const processedContent = preprocessContent(content);

  return (
    <div className={className}>
      <MathJaxContext config={config}>
        <ReactMarkdown
          remarkPlugins={[remarkMath]}
          rehypePlugins={[rehypeMathjax]}
          components={{
            // 自定义渲染器，保护数学公式不被转义
            p: ({ children }) => <p className="mb-2">{children}</p>,
            code: ({ children, className }) => {
              // 避免将数学公式当作代码块处理
              if (className?.includes('language-math')) {
                return <span>{children}</span>;
              }
              return <code className="bg-gray-100 px-1 py-0.5 rounded text-sm">{children}</code>;
            },
            pre: ({ children }) => <pre className="bg-gray-100 p-2 rounded overflow-x-auto">{children}</pre>,
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </MathJaxContext>
    </div>
  );
};
